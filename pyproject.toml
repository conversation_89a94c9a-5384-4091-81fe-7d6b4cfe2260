[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "passchanger"
version = "1.0.0"
description = "AI-powered password security management with local LLMs"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "PassChanger", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",

    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Security",
    "Topic :: System :: Systems Administration",
]
requires-python = ">=3.9"
dependencies = [
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.2",
    "selenium>=4.15.2",
    "playwright>=1.40.0",
    "pyyaml>=6.0.1",
    "cryptography>=41.0.7",
    "aiohttp>=3.9.1",
    "aiohttp-socks>=0.8.4",
    "python-dotenv>=1.0.0",
    "schedule>=1.2.0",
    "colorama>=0.4.6",
    "rich>=13.7.0",
    "pysocks>=1.7.1",
    "stem>=1.8.2",
    "ollama>=0.1.7",
    "langchain>=0.1.0",
    "langchain-community>=0.0.10",
    "pandas>=2.1.4",
    "numpy>=1.24.3",
    "python-dateutil>=2.8.2",
    "validators>=0.22.0",
    "fake-useragent>=1.4.0",
    "aiosqlite>=0.19.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]
test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
]

[project.scripts]
passchanger = "main:main"

[project.urls]
Homepage = "https://github.com/yourusername/passchanger"
Repository = "https://github.com/yourusername/passchanger"
Issues = "https://github.com/yourusername/passchanger/issues"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers"
testpaths = ["tests"]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "tor: marks tests that require Tor to be running",
]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "ollama.*",
    "fake_useragent.*",
    "stem.*",
    "validators.*",
]
ignore_missing_imports = true
