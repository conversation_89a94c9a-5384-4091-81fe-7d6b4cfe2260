# PassChanger - AI-Powered Password Security Management

PassChanger is an AI-based application that monitors your accounts for potential leaks and breaches, and helps manage password security using local LLMs only.

## Features

### Phase 1 - Leak Detection (Current)
- 🔍 **Multi-source leak detection**
  - HaveIBeenPwned API integration
  - Search engine monitoring
  - Dark web scanning (planned)
- 🤖 **AI-powered analysis** using local LLMs (Ollama)
- 📊 **Risk assessment** and prioritization
- 🔐 **Secure local storage** with encryption
- 📱 **Desktop notifications** for security alerts

### Phase 2 - Password Management (Planned)
- 🔄 **Automated password changing**
- 🛡️ **2FA setup assistance**
- 📋 **Account categorization** and management
- 🎯 **Targeted security recommendations**

## Prerequisites

1. **uv** (modern Python package manager)
   ```bash
   # Install uv
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. **Ollama** for local LLM support
   ```bash
   # Install Ollama
   curl -fsSL https://ollama.ai/install.sh | sh

   # Pull a model (e.g., llama2)
   ollama pull llama2
   ```

## Installation

### Quick Setup (Recommended)

1. **Clone or download the project**
   ```bash
   cd passchanger
   ```

2. **Run the setup script**
   ```bash
   python3 setup.py
   ```
   This will:
   - Check for uv and Ollama
   - Create a virtual environment
   - Install all dependencies
   - Test the installation

### Manual Setup

1. **Create virtual environment and install dependencies**
   ```bash
   uv venv
   uv pip install -e .
   ```

2. **Configure the application**
   - Edit `config.yaml` to match your preferences
   - Set your preferred LLM model in the config
   - Configure notification settings

3. **Run the application**
   ```bash
   uv run python main.py
   # OR activate the venv first:
   source .venv/bin/activate
   python main.py
   ```

## Configuration

### LLM Setup
Edit `config.yaml` to configure your local LLM:

```yaml
llm:
  provider: "ollama"
  model: "llama2"  # or mistral, codellama, etc.
  base_url: "http://localhost:11434"
```

### API Keys (Optional)
- **HaveIBeenPwned**: Add your API key for higher rate limits
- **Search APIs**: Configure if you have access to search APIs

### Security Settings
- Encryption keys are automatically generated
- Database is stored locally with encryption
- All sensitive data is encrypted at rest

## Usage

### Adding Accounts
1. Run the application: `python main.py`
2. Select "Add new account" from the menu
3. Enter account details (name, email, username, category)

### Running Leak Detection
1. Select "Run leak detection scan" from the menu
2. The system will check all configured sources
3. AI analysis will assess any findings
4. Results are stored and displayed with risk levels

### Viewing Results
- Check "View account status" for current security status
- Review the logs in `logs/passchanger.log`
- Database stores all historical data

## Security Features

- **Local-only processing**: No data sent to external AI services
- **Encrypted storage**: All sensitive data encrypted with Fernet
- **Secure deletion**: Files are securely overwritten before deletion
- **Rate limiting**: Respectful API usage with proper delays
- **Input sanitization**: Protection against injection attacks

## File Structure

```
passchanger/
├── main.py                 # Main application entry point
├── config.yaml            # Configuration file
├── requirements.txt       # Python dependencies
├── src/
│   ├── ai_engine.py       # Local LLM integration
│   ├── leak_detector.py   # Leak detection logic
│   ├── database.py        # Database management
│   ├── security_utils.py  # Security utilities
│   └── web_scraper.py     # Web scraping utilities
├── data/                  # Local data storage
├── logs/                  # Application logs
└── tests/                 # Test files
```

## Privacy & Security

- **No external AI services**: All AI processing happens locally
- **No cloud storage**: Everything stored locally on your machine
- **Encrypted data**: Sensitive information encrypted with strong keys
- **Minimal network requests**: Only necessary API calls for leak detection
- **User control**: You control all data and processing

## Troubleshooting

### Ollama Connection Issues
```bash
# Check if Ollama is running
ollama list

# Start Ollama service
ollama serve

# Test with a simple query
ollama run llama2 "Hello"
```

### Permission Issues
```bash
# Fix file permissions
chmod 600 data/encryption.key
chmod 755 main.py
```

### Dependencies
```bash
# Reinstall dependencies
pip install -r requirements.txt --force-reinstall
```

## Development

### Running Tests
```bash
# Install test dependencies
uv pip install -e ".[test]"

# Run all tests
uv run python -m pytest tests/

# Run only fast tests (skip slow integration tests)
uv run python -m pytest tests/ -m "not slow"

# Run only integration tests
uv run python -m pytest tests/ -m integration

# Run Tor integration tests (requires Tor running)
uv run python -m pytest tests/test_tor_integration.py -v

# Or use the convenience script
python tests/run_tor_tests.py
```

### Adding New Leak Sources
1. Create a new method in `LeakDetector`
2. Add configuration in `config.yaml`
3. Update the main scanning loop

### Extending AI Analysis
1. Modify prompts in `AIEngine`
2. Add new analysis methods
3. Update result processing

## Roadmap

### Phase 2 Features
- [ ] Automated password changing with Selenium/Playwright
- [ ] 2FA setup automation and guidance
- [ ] Browser extension for seamless integration
- [ ] Advanced account categorization
- [ ] Security score dashboard

### Phase 3 Features
- [ ] Mobile app companion
- [ ] Team/family account management
- [ ] Advanced threat intelligence
- [ ] Integration with password managers

## Contributing

This is a personal security tool. If you want to contribute:
1. Focus on security and privacy
2. Maintain local-only processing
3. Add comprehensive tests
4. Document security implications

## License

This project is for personal use. Please review and understand the code before using it with your sensitive information.

## Disclaimer

This tool is provided as-is for educational and personal use. Always verify results manually and maintain good security practices. The authors are not responsible for any security incidents or data loss.
