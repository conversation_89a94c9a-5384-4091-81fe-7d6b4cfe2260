#!/bin/bash
# PassChanger runner script using uv

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔐 PassChanger${NC}"
echo "=========================="

# Check if uv is installed
if ! command -v uv &> /dev/null; then
    echo -e "${RED}❌ uv is not installed${NC}"
    echo "Install it with: curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo -e "${YELLOW}⚠️  Virtual environment not found${NC}"
    echo "Run setup first: python3 setup.py"
    exit 1
fi

# Check if Ollama is running
if ! command -v ollama &> /dev/null; then
    echo -e "${YELLOW}⚠️  Ollama not found${NC}"
    echo "Install Ollama from: https://ollama.ai/"
elif ! ollama list &> /dev/null; then
    echo -e "${YELLOW}⚠️  Ollama service not running${NC}"
    echo "Start it with: ollama serve"
fi

# Run the application
echo -e "${GREEN}🚀 Starting PassChanger...${NC}"
uv run python main.py
