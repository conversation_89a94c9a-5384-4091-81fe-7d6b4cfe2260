#!/usr/bin/env python3
"""
Convenience script to run Tor integration tests
"""

import subprocess
import sys
from pathlib import Path

def main():
    """Run Tor integration tests with appropriate options"""
    print("🧅 PassChanger Tor Integration Tests")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("pyproject.toml").exists():
        print("❌ Please run this script from the project root directory")
        sys.exit(1)
    
    # Check if pytest is available
    try:
        subprocess.run(["python", "-m", "pytest", "--version"], 
                      capture_output=True, check=True)
    except subprocess.CalledProcessError:
        print("❌ pytest not found. Install test dependencies:")
        print("uv pip install -e '.[test]'")
        sys.exit(1)
    
    print("Running Tor integration tests...")
    print("Note: These tests require Tor to be running on 127.0.0.1:9050")
    print()
    
    # Run the tests
    cmd = [
        "python", "-m", "pytest", 
        "tests/test_tor_integration.py",
        "-v",
        "--tb=short",
        "-m", "integration"
    ]
    
    try:
        result = subprocess.run(cmd)
        sys.exit(result.returncode)
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        sys.exit(1)

if __name__ == "__main__":
    main()
