"""
Tor Integration Tests for PassChanger
Tests .onion site accessibility and Tor functionality
"""

import pytest
import asyncio
import aiohttp
import aiohttp_socks
import yaml
import time
from pathlib import Path
from typing import Dict, Any

# Test configuration for Tor integration
TOR_TEST_CONFIG = {
    'app': {
        'name': 'PassChanger',
        'version': '1.0.0',
        'debug': True,
        'log_level': 'INFO'
    },
    'leak_detection': {
        'enabled': True,
        'sources': {
            'darkweb': {
                'enabled': True,
                'sites': [
                    'http://3g2upl4pq6kufc4m.onion',  # DuckDuckGo (for testing)
                    'http://facebookwkhpilnemxj7asaniu7vnjjbiltxjqhye3mhbshg7kx5tfyd.onion',  # Facebook
                ],
                'tor_proxy': 'socks5://127.0.0.1:9050',
                'circuit_timeout': 30,
                'max_retries': 1,
                'tor_data_dir': 'data/tor',
                'auto_start': False
            }
        }
    }
}


class TestTorIntegration:
    """Test Tor integration and .onion site accessibility"""

    @pytest.fixture
    async def tor_manager(self):
        """Create TorManager instance for testing"""
        from src.tor_manager import TorManager

        manager = TorManager(TOR_TEST_CONFIG)
        await manager.initialize()
        yield manager
        await manager.close()

    @pytest.fixture
    async def tor_session(self):
        """Create Tor HTTP session for testing"""
        try:
            # Create SOCKS connector
            connector = aiohttp_socks.ProxyConnector.from_url('socks5://127.0.0.1:9050')

            session = aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=30),
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )

            yield session

        finally:
            if 'session' in locals():
                await session.close()

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_tor_connection(self, tor_session):
        """Test basic Tor connectivity using clearnet site"""
        try:
            async with tor_session.get('http://httpbin.org/ip', timeout=aiohttp.ClientTimeout(total=15)) as response:
                assert response.status == 200
                data = await response.json()

                # Verify we're using Tor (IP should be different from local IP)
                tor_ip = data.get('origin', '')
                assert tor_ip, "Should receive an IP address through Tor"
                print(f"✅ Tor connection working - Exit IP: {tor_ip}")

        except Exception as e:
            pytest.skip(f"Tor not available or not running: {e}")

    @pytest.mark.integration
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_onion_site_accessibility(self, tor_session):
        """Test .onion site accessibility"""
        # Load onion sites from config
        config_path = Path("config.yaml")
        if config_path.exists():
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)

            darkweb_config = config.get('leak_detection', {}).get('sources', {}).get('darkweb', {})
            onion_sites = darkweb_config.get('sites', [])
        else:
            # Use test configuration sites
            onion_sites = TOR_TEST_CONFIG['leak_detection']['sources']['darkweb']['sites']

        if not onion_sites:
            pytest.skip("No .onion sites configured for testing")

        results = []
        for site_url in onion_sites:
            # Skip comment lines and empty lines
            if site_url.strip().startswith('#') or not site_url.strip():
                continue

            # Clean up the URL (remove comments)
            url = site_url.split('#')[0].strip()
            if not url:
                continue

            result = await self._test_single_onion_site(tor_session, url)
            results.append(result)

            # Small delay between requests to be respectful
            await asyncio.sleep(2)

        # Analyze results
        successful = [r for r in results if r['success']]
        failed = [r for r in results if not r['success']]

        print(f"\n📊 Onion Site Test Summary:")
        print(f"✅ Successful: {len(successful)}/{len(results)}")
        print(f"❌ Failed: {len(failed)}/{len(results)}")

        if successful:
            print("\n✅ Working sites:")
            for result in successful:
                print(f"  • {result['url']} ({result['elapsed']:.2f}s)")

        if failed:
            print("\n❌ Failed sites:")
            for result in failed:
                print(f"  • {result['url']} - {result['status']}")

        # At least one site should be accessible if Tor is working properly
        # But we'll make this a soft assertion since .onion sites can be unreliable
        if len(results) > 0 and len(successful) == 0:
            pytest.skip("All .onion sites failed - this may be normal due to site availability")

    async def _test_single_onion_site(self, session: aiohttp.ClientSession, url: str, timeout: int = 30) -> Dict[str, Any]:
        """Test if a single .onion site is accessible"""
        try:
            print(f"Testing {url}...")
            start_time = time.time()

            async with session.get(url, timeout=aiohttp.ClientTimeout(total=timeout)) as response:
                elapsed = time.time() - start_time

                if response.status == 200:
                    content = await response.text()
                    print(f"✅ {url} - Status: {response.status} - Time: {elapsed:.2f}s - Size: {len(content)} bytes")
                    return {
                        'url': url,
                        'success': True,
                        'status': response.status,
                        'elapsed': elapsed,
                        'size': len(content)
                    }
                else:
                    print(f"⚠️  {url} - Status: {response.status} - Time: {elapsed:.2f}s")
                    return {
                        'url': url,
                        'success': False,
                        'status': response.status,
                        'elapsed': elapsed,
                        'size': 0
                    }

        except asyncio.TimeoutError:
            elapsed = time.time() - start_time
            print(f"❌ {url} - Timeout after {elapsed:.2f}s")
            return {
                'url': url,
                'success': False,
                'status': 'timeout',
                'elapsed': elapsed,
                'size': 0
            }

        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ {url} - Error: {e} - Time: {elapsed:.2f}s")
            return {
                'url': url,
                'success': False,
                'status': str(e),
                'elapsed': elapsed,
                'size': 0
            }

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_tor_manager_functionality(self, tor_manager):
        """Test TorManager class functionality"""
        # Test Tor status check
        is_running = await tor_manager.is_tor_running()
        print(f"Tor running status: {is_running}")

        # If Tor is not running, this is expected in CI/testing environments
        if not is_running:
            pytest.skip("Tor is not running - this is expected in testing environments")

        # Test new circuit creation (if Tor is running)
        if is_running:
            try:
                success = await tor_manager.new_circuit()
                # Circuit creation might fail if we don't have control access, that's OK
                print(f"New circuit creation: {'✅ Success' if success else '⚠️  Failed (may be expected)'}")
            except Exception as e:
                print(f"Circuit creation failed: {e} (this may be expected)")

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_leak_detector_tor_integration(self):
        """Test LeakDetector with Tor functionality"""
        from src.leak_detector import LeakDetector
        from src.tor_manager import TorManager

        # Create minimal components for testing
        tor_manager = TorManager(TOR_TEST_CONFIG)
        await tor_manager.initialize()

        try:
            # Create leak detector with Tor manager
            leak_detector = LeakDetector(
                config=TOR_TEST_CONFIG,
                ai_engine=None,  # Not needed for this test
                db_manager=None,  # Not needed for this test
                tor_manager=tor_manager
            )

            await leak_detector.initialize()

            # Test that Tor session creation works
            if await tor_manager.is_tor_running():
                assert leak_detector.tor_session is not None, "Tor session should be created when Tor is running"
                print("✅ LeakDetector Tor session created successfully")
            else:
                pytest.skip("Tor not running - cannot test Tor session creation")

        finally:
            if 'leak_detector' in locals():
                await leak_detector.close()
            await tor_manager.close()


if __name__ == "__main__":
    # Allow running this test file directly for debugging
    print("🧅 Running Tor Integration Tests")
    print("=" * 50)
    print("To run with pytest:")
    print("uv run python -m pytest tests/test_tor_integration.py -v")
    print("To run only fast tests:")
    print("uv run python -m pytest tests/test_tor_integration.py -v -m 'not slow'")
    print("To run all integration tests:")
    print("uv run python -m pytest tests/ -v -m integration")
