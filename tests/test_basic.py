"""
Basic tests for PassChanger components
"""

import pytest

class TestSecurityManager:
    """Test security utilities"""

    @pytest.mark.asyncio
    async def test_password_generation(self, security_manager):
        """Test password generation"""
        password = security_manager.generate_secure_password(12, True)

        assert len(password) == 12
        assert any(c.isupper() for c in password)
        assert any(c.islower() for c in password)
        assert any(c.isdigit() for c in password)

    @pytest.mark.asyncio
    async def test_encryption_decryption(self, security_manager):
        """Test data encryption and decryption"""
        test_data = "sensitive information"

        encrypted = security_manager.encrypt_data(test_data)
        decrypted = security_manager.decrypt_data(encrypted)

        assert decrypted == test_data
        assert encrypted != test_data

    @pytest.mark.asyncio
    async def test_password_strength_check(self, security_manager):
        """Test password strength assessment"""
        weak_password = "123"
        strong_password = "Str0ng!P@ssw0rd#2024"

        weak_result = security_manager.check_password_strength(weak_password)
        strong_result = security_manager.check_password_strength(strong_password)

        assert weak_result['strength'] in ['very_weak', 'weak']
        # Allow for 'fair' as well since password strength algorithms can vary
        assert strong_result['strength'] in ['fair', 'strong', 'very_strong']
        assert strong_result['score'] > weak_result['score']

    @pytest.mark.asyncio
    async def test_email_validation(self, security_manager):
        """Test email validation"""
        valid_email = "<EMAIL>"
        invalid_email = "not-an-email"

        assert security_manager.validate_email(valid_email) == True
        assert security_manager.validate_email(invalid_email) == False

class TestDatabaseManager:
    """Test database operations"""

    @pytest.mark.asyncio
    async def test_add_account(self, db_manager):
        """Test adding an account"""
        account_data = {
            'name': 'Test Account',
            'email': '<EMAIL>',
            'username': 'testuser',
            'category': 'standard'
        }

        account_id = await db_manager.add_account(account_data)
        assert account_id > 0

        # Verify account was added
        accounts = await db_manager.get_all_accounts()
        assert len(accounts) == 1
        assert accounts[0]['name'] == 'Test Account'
        assert accounts[0]['email'] == '<EMAIL>'

    @pytest.mark.asyncio
    async def test_leak_detection_recording(self, db_manager):
        """Test recording leak detections"""
        # First add an account
        account_data = {
            'name': 'Test Account',
            'email': '<EMAIL>',
            'category': 'standard'
        }
        account_id = await db_manager.add_account(account_data)

        # Record a leak detection
        detection_data = {
            'account_id': account_id,
            'source': 'test_source',
            'detection_type': 'test_breach',
            'risk_level': 'high',
            'confidence': 0.9,
            'description': 'Test leak detection'
        }

        detection_id = await db_manager.record_leak_detection(detection_data)
        assert detection_id > 0

        # Verify detection was recorded
        detections = await db_manager.get_recent_detections(7)
        assert len(detections) == 1
        assert detections[0]['risk_level'] == 'high'

def test_config_loading():
    """Test configuration loading"""
    import yaml

    # Test that our config file is valid YAML
    with open('config.yaml', 'r') as f:
        config = yaml.safe_load(f)

    assert 'app' in config
    assert 'llm' in config
    assert 'database' in config
    assert 'security' in config

def test_requirements():
    """Test that requirements.txt is valid"""
    with open('requirements.txt', 'r') as f:
        requirements = f.read().strip().split('\n')

    # Check that we have essential packages
    package_names = [req.split('==')[0].split('>=')[0] for req in requirements if req.strip()]

    essential_packages = ['requests', 'aiohttp', 'cryptography', 'pyyaml', 'ollama']
    for package in essential_packages:
        assert package in package_names, f"Missing essential package: {package}"

if __name__ == "__main__":
    # Run basic tests
    print("Running basic configuration tests...")

    test_config_loading()
    print("✅ Configuration file is valid")

    test_requirements()
    print("✅ Requirements file is valid")

    print("\nTo run full test suite:")
    print("pip install pytest pytest-asyncio")
    print("python -m pytest tests/ -v")
