"""
Shared pytest configuration and fixtures for PassChanger tests
"""

import pytest
import tempfile
from pathlib import Path

# Shared test configuration
TEST_CONFIG = {
    'app': {
        'name': 'PassChanger',
        'version': '1.0.0',
        'debug': True,
        'log_level': 'INFO'
    },
    'llm': {
        'provider': 'ollama',
        'model': 'llama2',
        'base_url': 'http://localhost:11434',
        'timeout': 30,
        'max_tokens': 2048
    },
    'database': {
        'type': 'sqlite',
        'path': ':memory:',  # Use in-memory database for tests
        'backup_interval': 24
    },
    'security': {
        'encryption_key_file': 'test_encryption.key',
        'password_length': 16,
        'password_complexity': True,
        'session_timeout': 3600
    },
    'leak_detection': {
        'enabled': True,
        'check_interval': 24,
        'sources': {
            'haveibeenpwned': {
                'enabled': False,  # Disable for tests
                'api_key': '',
                'user_agent': 'PassChanger-Test'
            },
            'darkweb': {
                'enabled': False,
                'sites': [
                    'http://3g2upl4pq6kufc4m.onion',  # DuckDuckGo (for testing)
                ],
                'tor_proxy': 'socks5://127.0.0.1:9050',
                'circuit_timeout': 30,
                'max_retries': 1
            },
            'custom_searches': {
                'enabled': False
            }
        }
    },
    'scraping': {
        'user_agents': ['Mozilla/5.0 (Test)'],
        'request_delay': 0.1,
        'max_retries': 1,
        'timeout': 5
    }
}

# Remove the custom event_loop fixture to avoid deprecation warning
# pytest-asyncio handles this automatically now

@pytest.fixture
def test_config():
    """Provide test configuration"""
    return TEST_CONFIG.copy()

@pytest.fixture
def temp_dir():
    """Provide a temporary directory for tests"""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)

@pytest.fixture
async def security_manager(test_config, temp_dir):
    """Create SecurityManager instance for testing"""
    from src.security_utils import SecurityManager

    config = test_config.copy()
    config['security']['encryption_key_file'] = str(temp_dir / 'test.key')

    manager = SecurityManager(config)
    await manager.initialize()
    yield manager

@pytest.fixture
async def db_manager(test_config):
    """Create DatabaseManager instance for testing"""
    from src.database import DatabaseManager

    manager = DatabaseManager(test_config)
    await manager.initialize()
    yield manager
    await manager.close()

def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "tor: marks tests that require Tor to be running"
    )

def pytest_collection_modifyitems(config, items):  # noqa: ARG001
    """Automatically mark tests based on their names and modules"""
    for item in items:
        # Mark integration tests
        if "integration" in item.nodeid or "tor" in item.nodeid:
            item.add_marker(pytest.mark.integration)

        # Mark slow tests
        if "slow" in item.name or "onion" in item.name:
            item.add_marker(pytest.mark.slow)

        # Mark Tor tests
        if "tor" in item.name or "onion" in item.name:
            item.add_marker(pytest.mark.tor)
